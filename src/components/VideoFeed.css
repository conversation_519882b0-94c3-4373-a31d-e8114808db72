.video-feed-container {
  background: #1a1a1a;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #333;
}

.video-feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #333;
}

.video-feed-header h3 {
  color: #fff;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.feed-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.feed-controls select {
  background: #2a2a2a;
  color: #fff;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 0.9rem;
  outline: none;
}

.feed-controls select:focus {
  border-color: #007bff;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #1e7e34;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.feed-status {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

.status-indicator.active {
  color: #28a745;
}

.status-indicator.inactive {
  color: #6c757d;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

.processing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffc107;
  font-size: 0.9rem;
  font-weight: 500;
}

.processing-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.video-display {
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-element {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  display: block;
}

.upload-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.file-input {
  display: none;
}

.upload-label {
  display: inline-block;
  padding: 16px 32px;
  background: #007bff;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.upload-label:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.error-message {
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-weight: 500;
  text-align: center;
  max-width: 80%;
}

.feed-statistics {
  margin-top: 16px;
  padding: 16px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #333;
}

.feed-statistics h4 {
  color: #fff;
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #1a1a1a;
  border-radius: 6px;
  border: 1px solid #444;
}

.stat-label {
  color: #bbb;
  font-size: 0.85rem;
  font-weight: 500;
}

.stat-value {
  color: #fff;
  font-size: 0.9rem;
  font-weight: 600;
}

.stat-value.critical {
  color: #dc3545;
}

.stat-value.warning {
  color: #ffc107;
}

.permission-help {
  margin-top: 16px;
  padding: 16px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #333;
}

.permission-help h4 {
  color: #fff;
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
}

.permission-help p {
  color: #bbb;
  margin: 0 0 12px 0;
  font-size: 0.9rem;
}

.permission-help ul {
  color: #bbb;
  margin: 0;
  padding-left: 20px;
  font-size: 0.9rem;
}

.permission-help li {
  margin-bottom: 4px;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-feed-container {
    padding: 16px;
    margin: 16px 0;
  }

  .video-feed-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .feed-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .video-element {
    max-height: 250px;
  }
}

@media (max-width: 480px) {
  .feed-controls {
    flex-direction: column;
    gap: 8px;
  }

  .feed-controls select,
  .btn {
    width: 100%;
    text-align: center;
  }

  .upload-label {
    padding: 12px 24px;
    font-size: 0.9rem;
  }
}
