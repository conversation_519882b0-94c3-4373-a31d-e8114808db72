/* Live Dashboard Styles */
.live-dashboard {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    color: #ffffff;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dashboard-header h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.live {
    background: #00ff00;
}

.status-dot.offline {
    background: #ff4444;
    animation: none;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Main Content */
.dashboard-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 0;
    overflow: hidden;
}

/* Sidebars */
.dashboard-sidebar,
.alerts-sidebar {
    background: #2a2a2a;
    padding: 1rem;
    overflow-y: auto;
    border-right: 1px solid #444;
}

.alerts-sidebar {
    border-right: none;
    border-left: 1px solid #444;
}

/* Panels */
.info-panel,
.stats-panel,
.controls-panel,
.zones-panel,
.test-panel,
.alerts-panel,
.responders-panel,
.zone-details-panel {
    background: #333;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #444;
}

.info-panel h3,
.stats-panel h3,
.controls-panel h3,
.zones-panel h3,
.test-panel h3,
.alerts-panel h3,
.responders-panel h3,
.zone-details-panel h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    color: #ffffff;
    border-bottom: 1px solid #555;
    padding-bottom: 0.5rem;
}

/* Statistics */
.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.stat-label {
    color: #ccc;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 600;
    color: #fff;
}

.stat-value.critical {
    color: #ff4444;
}

.stat-value.warning {
    color: #ffaa44;
}

/* Controls */
.control-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.control-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* Zones List */
.zones-list {
    max-height: 300px;
    overflow-y: auto;
}

.zone-item {
    background: #444;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.zone-item:hover {
    background: #555;
}

.zone-item.critical {
    border-left-color: #ff4444;
}

.zone-item.warning {
    border-left-color: #ffaa44;
}

.zone-item.normal {
    border-left-color: #44aa44;
}

.zone-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.zone-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #ccc;
}

.alert-level {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

.alert-level.critical {
    background: #ff4444;
    color: white;
}

.alert-level.warning {
    background: #ffaa44;
    color: white;
}

.alert-level.normal {
    background: #44aa44;
    color: white;
}

/* Test Controls */
.test-btn {
    background: #667eea;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    width: 100%;
    transition: background 0.3s ease;
}

.test-btn:hover {
    background: #5a6fd8;
}

/* Map Container */
.map-container {
    background: #000;
    position: relative;
    overflow: hidden;
}

/* Alerts */
.alerts-list,
.responders-list {
    max-height: 400px;
    overflow-y: auto;
}

.alert-item {
    background: #444;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid transparent;
}

.alert-item.high {
    border-left-color: #ff4444;
}

.alert-item.medium {
    border-left-color: #ffaa44;
}

.alert-item.low {
    border-left-color: #44aa44;
}

.alert-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.alert-type {
    font-weight: 600;
    font-size: 0.9rem;
}

.alert-time {
    font-size: 0.8rem;
    color: #ccc;
}

.alert-zone {
    font-size: 0.8rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.alert-message {
    font-size: 0.85rem;
    line-height: 1.3;
}

/* Responders */
.responder-item {
    background: #444;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.responder-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.responder-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.responder-status {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.responder-status.active {
    background: #44aa44;
    color: white;
}

.responder-status.responding {
    background: #ffaa44;
    color: white;
}

.responder-status.standby {
    background: #666;
    color: white;
}

.responder-status.patrol {
    background: #667eea;
    color: white;
}

.responder-type {
    font-size: 0.8rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.responder-time {
    font-size: 0.75rem;
    color: #888;
}

/* Zone Details */
.zone-details h4 {
    margin: 0 0 1rem 0;
    color: #fff;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.close-details {
    background: #666;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
    margin-top: 1rem;
}

.close-details:hover {
    background: #777;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-content {
        grid-template-columns: 250px 1fr 250px;
    }
}

@media (max-width: 768px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }

    .dashboard-sidebar,
    .alerts-sidebar {
        max-height: 200px;
        overflow-y: auto;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #333;
}

::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Video Integration Styles */
.video-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.video-btn:hover {
    background: linear-gradient(135deg, #ff5252, #d63031);
}

.map-video-split {
    display: flex;
    height: 100%;
    gap: 16px;
    padding: 16px;
    background: #1a1a1a;
}

.video-section {
    flex: 1;
    min-width: 400px;
    display: flex;
    flex-direction: column;
}

.map-section {
    flex: 1.5;
    min-width: 500px;
    display: flex;
    flex-direction: column;
}

.video-status-panel {
    background: #333;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #444;
}

.video-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-label {
    color: #ccc;
    font-size: 0.9rem;
}

.status-value {
    font-weight: 600;
    color: #fff;
    font-size: 0.9rem;
}

.status-value.processing {
    color: #28a745;
}

.status-value.stopped {
    color: #dc3545;
}

.status-value.initialized {
    color: #ffc107;
}

/* Responsive adjustments for video layout */
@media (max-width: 1024px) {
    .map-video-split {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }

    .video-section,
    .map-section {
        min-width: auto;
        flex: none;
    }

    .video-section {
        order: 1;
    }

    .map-section {
        order: 2;
        height: 400px;
    }
}

@media (max-width: 768px) {
    .header-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .map-video-split {
        padding: 8px;
        gap: 8px;
    }

    .video-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}
